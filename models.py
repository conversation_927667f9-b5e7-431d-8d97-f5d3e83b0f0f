from dataclasses import dataclass
from typing import Optional
from datetime import datetime

@dataclass
class Product:
    id: Optional[int] = None
    name: str = ""
    category: str = ""
    price: float = 0.0
    quantity: int = 0
    min_quantity: int = 10
    supplier: str = ""
    expiry_date: str = ""
    created_at: Optional[str] = None

@dataclass
class Customer:
    id: Optional[int] = None
    name: str = ""
    phone: str = ""
    address: str = ""
    created_at: Optional[str] = None

@dataclass
class Sale:
    id: Optional[int] = None
    customer_id: Optional[int] = None
    total_amount: float = 0.0
    sale_date: Optional[str] = None

@dataclass
class SaleItem:
    id: Optional[int] = None
    sale_id: Optional[int] = None
    product_id: int = 0
    quantity: int = 0
    unit_price: float = 0.0
    total_price: float = 0.0

# فئات المنتجات الزراعية
PRODUCT_CATEGORIES = [
    "مبيدات حشرية",
    "مبيدات فطرية", 
    "مبيدات عشبية",
    "أسمدة كيماوية",
    "أسمدة عضوية",
    "بذور",
    "أدوات زراعية",
    "مستلزمات ري",
    "أخرى"
]

# وحدات القياس
UNITS = [
    "كيلو",
    "جرام",
    "لتر",
    "مليلتر",
    "قطعة",
    "عبوة",
    "كيس"
]
