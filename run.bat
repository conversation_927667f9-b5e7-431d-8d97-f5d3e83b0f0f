@echo off
chcp 65001 > nul
title برنامج الصيدلية الزراعية

echo.
echo ========================================
echo    برنامج إدارة الصيدلية الزراعية
echo    Agricultural Pharmacy Management
echo ========================================
echo.

REM التحقق من وجود Python
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo.

REM التحقق من وجود الملفات المطلوبة
if not exist "main.py" (
    echo ❌ ملف main.py غير موجود
    pause
    exit /b 1
)

if not exist "gui.py" (
    echo ❌ ملف gui.py غير موجود
    pause
    exit /b 1
)

if not exist "database.py" (
    echo ❌ ملف database.py غير موجود
    pause
    exit /b 1
)

echo ✅ جميع الملفات المطلوبة موجودة
echo.

REM سؤال عن إضافة بيانات تجريبية
if not exist "pharmacy.db" (
    echo 📊 لا توجد قاعدة بيانات مسبقة
    set /p add_sample="هل تريد إضافة بيانات تجريبية؟ (y/n): "
    if /i "!add_sample!"=="y" (
        echo.
        echo 📦 إضافة بيانات تجريبية...
        python sample_data.py
        echo.
    )
)

echo 🚀 تشغيل البرنامج...
echo.
python main.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل البرنامج
    pause
)

echo.
echo 👋 شكراً لاستخدام برنامج الصيدلية الزراعية
pause
