#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة الصيدلية الزراعية
Agricultural Pharmacy Management System

المطور: مساعد الذكي
التاريخ: 2024
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# إضافة المجلد الحالي لمسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from gui import PharmacyGUI
    from database import Database
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        
        # تعيين الخط العربي إذا كان متوفراً
        try:
            root.option_add('*Font', 'Arial 10')
        except:
            pass
        
        # إنشاء واجهة المستخدم
        app = PharmacyGUI(root)
        
        # تشغيل البرنامج
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ في تشغيل البرنامج:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
