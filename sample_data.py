#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية لبرنامج الصيدلية الزراعية
Sample data for Agricultural Pharmacy Management System
"""

from database import Database
from datetime import datetime, timedelta
import random

def add_sample_products(db):
    """إضافة منتجات تجريبية"""
    print("📦 إضافة منتجات تجريبية...")
    
    sample_products = [
        # مبيدات حشرية
        ("أكتارا 25% WG", "مبيدات حشرية", 45.50, 25, 5, "شركة سينجنتا", "2025-12-31"),
        ("كونفيدور 20% SL", "مبيدات حشرية", 38.75, 30, 8, "شركة باير", "2025-10-15"),
        ("لانيت 90% SP", "مبيدات حشرية", 22.00, 40, 10, "شركة دوبونت", "2025-08-20"),
        ("مارشال 25% EC", "مبيدات حشرية", 35.25, 20, 5, "شركة FMC", "2025-11-30"),
        
        # مبيدات فطرية
        ("ريدوميل جولد 68% WG", "مبيدات فطرية", 52.00, 18, 3, "شركة سينجنتا", "2025-09-15"),
        ("أنتراكول 70% WP", "مبيدات فطرية", 28.50, 35, 8, "شركة باير", "2025-07-10"),
        ("توبسين إم 70% WP", "مبيدات فطرية", 41.75, 22, 5, "شركة نيبون سودا", "2025-12-01"),
        
        # مبيدات عشبية
        ("راوند أب 48% SL", "مبيدات عشبية", 33.00, 45, 10, "شركة مونسانتو", "2025-06-30"),
        ("جراموكسون 20% SL", "مبيدات عشبية", 29.25, 28, 6, "شركة سينجنتا", "2025-08-15"),
        ("سلكت سوبر 12.5% EC", "مبيدات عشبية", 48.50, 15, 3, "شركة أريستا", "2025-10-20"),
        
        # أسمدة كيماوية
        ("يوريا 46%", "أسمدة كيماوية", 15.75, 100, 20, "شركة سابك", "2026-12-31"),
        ("سوبر فوسفات 16%", "أسمدة كيماوية", 12.50, 80, 15, "شركة المعادن", "2026-11-30"),
        ("سلفات البوتاسيوم 50%", "أسمدة كيماوية", 18.25, 60, 12, "شركة الراجحي", "2026-10-15"),
        ("NPK 20-20-20", "أسمدة كيماوية", 25.00, 45, 10, "شركة يارا", "2026-09-30"),
        
        # أسمدة عضوية
        ("كمبوست عضوي", "أسمدة عضوية", 8.50, 200, 30, "مزرعة الخير", "2026-06-30"),
        ("سماد دواجن مخمر", "أسمدة عضوية", 6.75, 150, 25, "مزرعة النور", "2026-08-15"),
        ("هيوميك أسيد", "أسمدة عضوية", 32.00, 25, 5, "شركة أجريكولا", "2025-12-31"),
        
        # بذور
        ("بذور طماطم هجين", "بذور", 125.00, 12, 2, "شركة سيمينيس", "2025-03-31"),
        ("بذور خيار هجين", "بذور", 95.50, 15, 3, "شركة رايك زوان", "2025-04-15"),
        ("بذور فلفل حلو", "بذور", 110.75, 8, 2, "شركة إنزا زادن", "2025-02-28"),
        ("بذور باذنجان", "بذور", 85.25, 10, 2, "شركة تاكي", "2025-05-30"),
        
        # أدوات زراعية
        ("مرش ظهري 16 لتر", "أدوات زراعية", 185.00, 8, 2, "شركة جاكتو", ""),
        ("مقص تقليم", "أدوات زراعية", 45.50, 15, 3, "شركة فيلكو", ""),
        ("شوكة زراعية", "أدوات زراعية", 25.75, 20, 5, "شركة أدوات الحديقة", ""),
        ("مجرفة صغيرة", "أدوات زراعية", 18.25, 25, 5, "شركة جاردن تولز", ""),
        
        # مستلزمات ري
        ("خرطوم ري 25 متر", "مستلزمات ري", 75.00, 12, 3, "شركة فليكس", ""),
        ("رشاش دوار", "مستلزمات ري", 35.50, 18, 4, "شركة ريين بيرد", ""),
        ("تايمر ري أوتوماتيكي", "مستلزمات ري", 125.75, 6, 1, "شركة هانتر", ""),
        ("قطارة تعويض ضغط", "مستلزمات ري", 2.25, 500, 50, "شركة نتافيم", ""),
    ]
    
    for product in sample_products:
        try:
            db.add_product(*product)
        except Exception as e:
            print(f"خطأ في إضافة المنتج {product[0]}: {e}")
    
    print(f"✅ تم إضافة {len(sample_products)} منتج تجريبي")

def add_sample_customers(db):
    """إضافة عملاء تجريبيين"""
    print("👥 إضافة عملاء تجريبيين...")
    
    sample_customers = [
        ("أحمد محمد الفلاح", "0501234567", "مزرعة الورود - طريق الرياض الخرج"),
        ("فاطمة علي الزراعية", "0551234567", "مزرعة النخيل - حي الملك فهد"),
        ("محمد سعد البستاني", "0561234567", "مشتل الأمل - شارع الملك عبدالعزيز"),
        ("نورا أحمد الحديقة", "0571234567", "حديقة الورود - حي النزهة"),
        ("خالد عبدالله المزارع", "0581234567", "مزرعة الخضار - طريق الدمام"),
        ("سارة محمد النباتات", "0591234567", "مشتل الياسمين - حي الروضة"),
        ("عبدالرحمن سالم", "0501234568", "مزرعة التمور - وادي الدواسر"),
        ("أمل فهد الزهور", "0551234568", "محل الزهور الجميلة - وسط البلد"),
        ("يوسف عمر الحقول", "0561234568", "مزرعة الحقول الخضراء - القصيم"),
        ("ريم سعود البساتين", "0571234568", "بستان الفواكه - الطائف"),
    ]
    
    for customer in sample_customers:
        try:
            db.add_customer(*customer)
        except Exception as e:
            print(f"خطأ في إضافة العميل {customer[0]}: {e}")
    
    print(f"✅ تم إضافة {len(sample_customers)} عميل تجريبي")

def add_sample_sales(db):
    """إضافة مبيعات تجريبية"""
    print("💰 إضافة مبيعات تجريبية...")
    
    # الحصول على المنتجات والعملاء
    products = db.get_all_products()
    customers = db.get_all_customers()
    
    if not products or not customers:
        print("❌ لا توجد منتجات أو عملاء لإنشاء مبيعات تجريبية")
        return
    
    # إنشاء مبيعات عشوائية للأيام الماضية
    for i in range(15):  # 15 عملية بيع
        # اختيار عميل عشوائي
        customer = random.choice(customers)
        customer_id = customer[0]
        
        # إنشاء سلة عشوائية
        cart_items = []
        num_items = random.randint(1, 4)  # من 1 إلى 4 منتجات في الفاتورة
        
        selected_products = random.sample(products, min(num_items, len(products)))
        
        for product in selected_products:
            product_id = product[0]
            price = product[3]
            available_qty = product[4]
            
            if available_qty > 0:
                quantity = random.randint(1, min(5, available_qty))
                cart_items.append({
                    'product_id': product_id,
                    'quantity': quantity,
                    'price': price
                })
        
        if cart_items:
            try:
                sale_id = db.add_sale(customer_id, cart_items)
                print(f"✅ تم إضافة فاتورة رقم {sale_id}")
            except Exception as e:
                print(f"❌ خطأ في إضافة البيع: {e}")
    
    print("✅ تم إضافة المبيعات التجريبية")

def main():
    """الدالة الرئيسية لإضافة البيانات التجريبية"""
    print("🌱 إضافة بيانات تجريبية لبرنامج الصيدلية الزراعية")
    print("=" * 60)
    
    # إنشاء قاعدة البيانات
    db = Database()
    
    # التحقق من وجود بيانات مسبقة
    existing_products = db.get_all_products()
    if existing_products:
        response = input("⚠️  توجد بيانات مسبقة. هل تريد المتابعة؟ (y/n): ").strip().lower()
        if response not in ['y', 'yes', 'نعم']:
            print("❌ تم إلغاء العملية")
            return
    
    try:
        # إضافة البيانات التجريبية
        add_sample_products(db)
        add_sample_customers(db)
        add_sample_sales(db)
        
        print("\n🎉 تم إضافة جميع البيانات التجريبية بنجاح!")
        print("💡 يمكنك الآن تشغيل البرنامج الرئيسي لاستكشاف الميزات")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ أثناء إضافة البيانات: {e}")

if __name__ == "__main__":
    main()
