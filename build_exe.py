#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لتحويل برنامج الصيدلية الزراعية إلى ملف exe
Build script to convert Agricultural Pharmacy program to exe
"""

import os
import sys
import subprocess
from pathlib import Path

def install_pyinstaller():
    """تثبيت PyInstaller إذا لم يكن مثبتاً"""
    try:
        import PyInstaller
        print("✅ PyInstaller مثبت بالفعل")
        return True
    except ImportError:
        print("📦 تثبيت PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ تم تثبيت PyInstaller بنجاح")
            return True
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت PyInstaller")
            return False

def create_spec_file():
    """إنشاء ملف spec مخصص"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.simpledialog',
        'sqlite3',
        'datetime',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='الصيدلية_الزراعية',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('pharmacy.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف pharmacy.spec")

def build_exe():
    """بناء ملف exe"""
    print("🔨 بدء عملية البناء...")
    
    # التأكد من وجود الملفات المطلوبة
    required_files = ['main.py', 'gui.py', 'database.py', 'models.py']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ الملفات التالية مفقودة: {', '.join(missing_files)}")
        return False
    
    # إنشاء ملف spec
    create_spec_file()
    
    # تشغيل PyInstaller
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "pharmacy.spec"
        ]
        
        print("🚀 تشغيل PyInstaller...")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ تم بناء البرنامج بنجاح!")
            
            # التحقق من وجود الملف المبني
            exe_path = Path("dist/الصيدلية_الزراعية.exe")
            if exe_path.exists():
                print(f"📁 مسار الملف: {exe_path.absolute()}")
                print(f"📊 حجم الملف: {exe_path.stat().st_size / (1024*1024):.1f} MB")
                return True
            else:
                print("❌ لم يتم العثور على الملف المبني")
                return False
        else:
            print("❌ فشل في بناء البرنامج")
            print("خطأ:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ حدث خطأ أثناء البناء: {e}")
        return False

def create_simple_build():
    """إنشاء نسخة مبسطة من البرنامج"""
    print("🔨 إنشاء نسخة مبسطة...")
    
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed",
            "--name=Agricultural_Pharmacy",
            "--clean",
            "--noconfirm",
            "main.py"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم إنشاء النسخة المبسطة بنجاح!")
            
            exe_path = Path("dist/Agricultural_Pharmacy.exe")
            if exe_path.exists():
                print(f"📁 مسار الملف: {exe_path.absolute()}")
                print(f"📊 حجم الملف: {exe_path.stat().st_size / (1024*1024):.1f} MB")
                return True
        
        print("❌ فشل في إنشاء النسخة المبسطة")
        return False
        
    except Exception as e:
        print(f"❌ حدث خطأ: {e}")
        return False

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("🧹 تنظيف الملفات المؤقتة...")
    
    cleanup_paths = ['build', '__pycache__', '*.spec']
    
    for path in cleanup_paths:
        if path.endswith('*'):
            # حذف الملفات بامتداد معين
            import glob
            for file in glob.glob(path):
                try:
                    os.remove(file)
                    print(f"🗑️  تم حذف {file}")
                except:
                    pass
        else:
            # حذف المجلدات
            import shutil
            if os.path.exists(path):
                try:
                    shutil.rmtree(path)
                    print(f"🗑️  تم حذف مجلد {path}")
                except:
                    pass

def main():
    """الدالة الرئيسية"""
    print("🏗️  برنامج بناء الصيدلية الزراعية")
    print("=" * 50)
    
    # تثبيت PyInstaller
    if not install_pyinstaller():
        return
    
    print("\nاختر نوع البناء:")
    print("1. بناء متقدم (ملف spec مخصص)")
    print("2. بناء مبسط (ملف واحد)")
    print("3. تنظيف الملفات المؤقتة فقط")
    
    choice = input("\nأدخل اختيارك (1-3): ").strip()
    
    if choice == "1":
        success = build_exe()
    elif choice == "2":
        success = create_simple_build()
    elif choice == "3":
        cleanup()
        return
    else:
        print("❌ اختيار غير صحيح")
        return
    
    if success:
        print("\n🎉 تم الانتهاء بنجاح!")
        print("💡 يمكنك الآن تشغيل البرنامج من مجلد dist")
        
        # سؤال عن التنظيف
        clean = input("\nهل تريد تنظيف الملفات المؤقتة؟ (y/n): ").strip().lower()
        if clean in ['y', 'yes', 'نعم']:
            cleanup()
    else:
        print("\n❌ فشل في البناء")

if __name__ == "__main__":
    main()
