#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة التقارير للصيدلية الزراعية
Reports module for Agricultural Pharmacy
"""

from datetime import datetime, date
import os

class ReportGenerator:
    def __init__(self, database):
        self.db = database
    
    def generate_inventory_report(self, save_to_file=False):
        """إنشاء تقرير المخزون"""
        products = self.db.get_all_products()
        
        report_lines = []
        report_lines.append("تقرير المخزون")
        report_lines.append("=" * 80)
        report_lines.append(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # عناوين الأعمدة
        header = f"{'الرقم':<5} {'اسم المنتج':<30} {'الفئة':<15} {'الكمية':<8} {'السعر':<10} {'القيمة':<12}"
        report_lines.append(header)
        report_lines.append("-" * 80)
        
        total_value = 0
        total_items = 0
        
        for product in products:
            product_id = product[0]
            name = product[1][:28]  # قطع الاسم إذا كان طويلاً
            category = product[2][:13]
            quantity = product[4]
            price = product[3]
            value = quantity * price
            
            total_value += value
            total_items += quantity
            
            line = f"{product_id:<5} {name:<30} {category:<15} {quantity:<8} {price:<10.2f} {value:<12.2f}"
            report_lines.append(line)
        
        # الإجماليات
        report_lines.append("-" * 80)
        report_lines.append(f"إجمالي عدد المنتجات: {len(products)}")
        report_lines.append(f"إجمالي الكميات: {total_items}")
        report_lines.append(f"إجمالي قيمة المخزون: {total_value:.2f}")
        
        report_text = "\n".join(report_lines)
        
        if save_to_file:
            filename = f"inventory_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_text)
            return filename
        
        return report_text
    
    def generate_low_stock_report(self, save_to_file=False):
        """إنشاء تقرير المنتجات منخفضة المخزون"""
        low_stock_products = self.db.get_low_stock_products()
        
        report_lines = []
        report_lines.append("تقرير المنتجات منخفضة المخزون")
        report_lines.append("=" * 70)
        report_lines.append(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        if not low_stock_products:
            report_lines.append("✅ جميع المنتجات متوفرة بكميات كافية")
        else:
            report_lines.append(f"⚠️  يوجد {len(low_stock_products)} منتج منخفض المخزون")
            report_lines.append("")
            
            # عناوين الأعمدة
            header = f"{'الرقم':<5} {'اسم المنتج':<35} {'الكمية الحالية':<15} {'الحد الأدنى':<12}"
            report_lines.append(header)
            report_lines.append("-" * 67)
            
            for product in low_stock_products:
                product_id = product[0]
                name = product[1][:33]
                current_qty = product[4]
                min_qty = product[5]
                
                line = f"{product_id:<5} {name:<35} {current_qty:<15} {min_qty:<12}"
                report_lines.append(line)
        
        report_text = "\n".join(report_lines)
        
        if save_to_file:
            filename = f"low_stock_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_text)
            return filename
        
        return report_text
    
    def generate_sales_report(self, start_date=None, end_date=None, save_to_file=False):
        """إنشاء تقرير المبيعات"""
        sales = self.db.get_sales_report(start_date, end_date)
        
        report_lines = []
        report_lines.append("تقرير المبيعات")
        report_lines.append("=" * 80)
        report_lines.append(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if start_date and end_date:
            report_lines.append(f"الفترة: من {start_date} إلى {end_date}")
        else:
            report_lines.append("الفترة: جميع المبيعات")
        
        report_lines.append("")
        
        if not sales:
            report_lines.append("لا توجد مبيعات في الفترة المحددة")
        else:
            # عناوين الأعمدة
            header = f"{'رقم الفاتورة':<12} {'العميل':<25} {'المبلغ':<12} {'التاريخ':<20}"
            report_lines.append(header)
            report_lines.append("-" * 69)
            
            total_sales = 0
            for sale in sales:
                sale_id = sale[0]
                customer_name = sale[1][:23] if sale[1] else "غير محدد"
                amount = sale[2]
                sale_date = sale[3][:19]  # عرض التاريخ والوقت
                
                total_sales += amount
                
                line = f"{sale_id:<12} {customer_name:<25} {amount:<12.2f} {sale_date:<20}"
                report_lines.append(line)
            
            # الإجماليات
            report_lines.append("-" * 69)
            report_lines.append(f"عدد الفواتير: {len(sales)}")
            report_lines.append(f"إجمالي المبيعات: {total_sales:.2f}")
            
            # متوسط قيمة الفاتورة
            avg_sale = total_sales / len(sales) if sales else 0
            report_lines.append(f"متوسط قيمة الفاتورة: {avg_sale:.2f}")
        
        report_text = "\n".join(report_lines)
        
        if save_to_file:
            period_suffix = f"_{start_date}_to_{end_date}" if start_date and end_date else "_all"
            filename = f"sales_report{period_suffix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_text)
            return filename
        
        return report_text
    
    def generate_daily_summary(self, target_date=None, save_to_file=False):
        """إنشاء ملخص يومي"""
        if not target_date:
            target_date = date.today().strftime('%Y-%m-%d')
        
        # مبيعات اليوم
        sales = self.db.get_sales_report(target_date, target_date)
        
        # منتجات منخفضة المخزون
        low_stock = self.db.get_low_stock_products()
        
        report_lines = []
        report_lines.append(f"الملخص اليومي - {target_date}")
        report_lines.append("=" * 50)
        report_lines.append("")
        
        # ملخص المبيعات
        report_lines.append("📊 ملخص المبيعات:")
        if sales:
            total_sales = sum(sale[2] for sale in sales)
            report_lines.append(f"   • عدد الفواتير: {len(sales)}")
            report_lines.append(f"   • إجمالي المبيعات: {total_sales:.2f}")
        else:
            report_lines.append("   • لا توجد مبيعات اليوم")
        
        report_lines.append("")
        
        # تنبيهات المخزون
        report_lines.append("⚠️  تنبيهات المخزون:")
        if low_stock:
            report_lines.append(f"   • {len(low_stock)} منتج منخفض المخزون")
            for product in low_stock[:5]:  # عرض أول 5 منتجات فقط
                report_lines.append(f"     - {product[1]}: {product[4]} متبقي")
            if len(low_stock) > 5:
                report_lines.append(f"     ... و {len(low_stock) - 5} منتجات أخرى")
        else:
            report_lines.append("   • جميع المنتجات متوفرة بكميات كافية")
        
        report_text = "\n".join(report_lines)
        
        if save_to_file:
            filename = f"daily_summary_{target_date}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_text)
            return filename
        
        return report_text
