# دليل البدء السريع - برنامج الصيدلية الزراعية

## 🚀 التشغيل السريع

### الطريقة الأولى: استخدام ملف التشغيل
1. انقر نقراً مزدوجاً على `run.bat`
2. اتبع التعليمات على الشاشة

### الطريقة الثانية: من سطر الأوامر
```bash
python main.py
```

## 📊 إضافة بيانات تجريبية
إذا كنت تريد تجربة البرنامج مع بيانات جاهزة:
```bash
python sample_data.py
```

## 🔧 تحويل إلى ملف exe
```bash
python build_exe.py
```

## 📋 الاستخدام الأساسي

### 1. إضافة منتج جديد
- اذهب إلى تبويب "إدارة المنتجات"
- ا<PERSON><PERSON><PERSON> "إضافة منتج"
- املأ البيانات واضغط "حفظ"

### 2. عملية بيع
- اذهب إلى تبويب "المبيعات"
- اختر المنتجات من القائمة العلوية
- حدد الكمية واضغط "إضافة للسلة"
- اضغط "إتمام البيع"

### 3. عرض التقارير
- اذهب إلى تبويب "التقارير"
- اختر نوع التقرير المطلوب

## 🎯 نصائح سريعة

- **البحث**: استخدم مربع البحث في تبويب المنتجات
- **التنبيهات**: المنتجات منخفضة المخزون تظهر بلون أحمر
- **النسخ الاحتياطي**: انسخ ملف `pharmacy.db` بانتظام
- **الأمان**: احتفظ بنسخة من مجلد البرنامج كاملاً

## ❓ مشاكل شائعة

**البرنامج لا يبدأ:**
- تأكد من تثبيت Python 3.8+
- تحقق من وجود جميع الملفات

**خطأ في قاعدة البيانات:**
- احذف ملف `pharmacy.db`
- أعد تشغيل البرنامج

**مشكلة في التحويل إلى exe:**
- ثبت pyinstaller: `pip install pyinstaller`
- استخدم سكريبت البناء المرفق
