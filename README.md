# برنامج إدارة الصيدلية الزراعية
## Agricultural Pharmacy Management System

برنامج شامل وبسيط لإدارة الصيدليات الزراعية مطور بلغة Python مع واجهة مستخدم رسومية.

## 🌟 الميزات الرئيسية

### 📦 إدارة المخزون
- إضافة وتعديل وحذف المنتجات الزراعية
- تصنيف المنتجات (مبيدات، أسمدة، بذور، إلخ)
- تتبع الكميات والأسعار
- تنبيهات للمنتجات منخفضة المخزون
- تواريخ انتهاء الصلاحية

### 💰 إدارة المبيعات
- نظام سلة تسوق سهل الاستخدام
- ربط المبيعات بالعملاء
- تحديث المخزون تلقائياً عند البيع
- حساب المجاميع والضرائب

### 👥 إدارة العملاء
- قاعدة بيانات العملاء
- معلومات الاتصال والعناوين
- تاريخ التعاملات

### 📊 التقارير والإحصائيات
- تقرير المخزون الشامل
- تقرير المنتجات منخفضة المخزون
- تقرير المبيعات اليومية والشهرية
- ملخص يومي للعمليات

## 🛠️ متطلبات التشغيل

### متطلبات النظام
- Windows 10 أو أحدث
- Python 3.8 أو أحدث (للتطوير)
- 100 MB مساحة فارغة على القرص الصلب

### المكتبات المطلوبة
```
tkinter (مدمجة مع Python)
sqlite3 (مدمجة مع Python)
datetime (مدمجة مع Python)
pyinstaller (لتحويل البرنامج إلى exe)
```

## 🚀 طريقة التثبيت والتشغيل

### الطريقة الأولى: تشغيل من الكود المصدري
1. تأكد من تثبيت Python 3.8+
2. حمل جميع ملفات البرنامج
3. شغل الأمر التالي:
```bash
python main.py
```

### الطريقة الثانية: تحويل إلى ملف exe
1. شغل سكريبت البناء:
```bash
python build_exe.py
```
2. اختر نوع البناء المطلوب
3. ستجد الملف التنفيذي في مجلد `dist`

## 📁 هيكل الملفات

```
accounting/
├── main.py              # الملف الرئيسي
├── gui.py               # واجهة المستخدم الرسومية
├── database.py          # إدارة قاعدة البيانات
├── models.py            # نماذج البيانات
├── reports.py           # وحدة التقارير
├── build_exe.py         # سكريبت تحويل إلى exe
├── requirements.txt     # المتطلبات
├── README.md           # هذا الملف
└── pharmacy.db         # قاعدة البيانات (تُنشأ تلقائياً)
```

## 🎯 كيفية الاستخدام

### 1. إدارة المنتجات
- انتقل إلى تبويب "إدارة المنتجات"
- اضغط "إضافة منتج" لإضافة منتج جديد
- املأ البيانات المطلوبة (الاسم، الفئة، السعر، الكمية)
- يمكنك البحث والفلترة حسب الفئة

### 2. عملية البيع
- انتقل إلى تبويب "المبيعات"
- اختر المنتجات من القائمة العلوية
- حدد الكمية واضغط "إضافة للسلة"
- راجع السلة واضغط "إتمام البيع"

### 3. إدارة العملاء
- انتقل إلى تبويب "العملاء"
- اضغط "إضافة عميل" لإضافة عميل جديد
- املأ بيانات العميل (الاسم، الهاتف، العنوان)

### 4. عرض التقارير
- انتقل إلى تبويب "التقارير"
- اختر نوع التقرير المطلوب
- ستظهر النتائج في منطقة النص

## 🔧 الإعدادات والتخصيص

### فئات المنتجات المتاحة
- مبيدات حشرية
- مبيدات فطرية
- مبيدات عشبية
- أسمدة كيماوية
- أسمدة عضوية
- بذور
- أدوات زراعية
- مستلزمات ري
- أخرى

### تخصيص الحد الأدنى للمخزون
يمكن تعيين حد أدنى مختلف لكل منتج لتلقي تنبيهات عندما ينخفض المخزون.

## 🛡️ الأمان وحفظ البيانات

- جميع البيانات محفوظة في قاعدة بيانات SQLite محلية
- يُنصح بعمل نسخ احتياطية دورية من ملف `pharmacy.db`
- البرنامج لا يتطلب اتصال بالإنترنت

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**البرنامج لا يبدأ:**
- تأكد من تثبيت Python بشكل صحيح
- تحقق من وجود جميع الملفات المطلوبة

**خطأ في قاعدة البيانات:**
- احذف ملف `pharmacy.db` وأعد تشغيل البرنامج
- سيتم إنشاء قاعدة بيانات جديدة تلقائياً

**مشاكل في التحويل إلى exe:**
- تأكد من تثبيت pyinstaller: `pip install pyinstaller`
- جرب البناء المبسط من خلال سكريبت البناء

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع قسم استكشاف الأخطاء أعلاه
- تأكد من تحديث Python إلى أحدث إصدار
- تحقق من صحة جميع الملفات المطلوبة

## 📝 الترخيص

هذا البرنامج مجاني للاستخدام الشخصي والتجاري.

## 🔄 التحديثات المستقبلية

الميزات المخطط إضافتها:
- طباعة الفواتير
- تصدير التقارير إلى PDF
- نظام المستخدمين والصلاحيات
- ربط مع أنظمة الدفع الإلكتروني
- تطبيق موبايل مصاحب

---

**تم تطوير هذا البرنامج بواسطة الذكي الاصطناعي لخدمة المجتمع الزراعي** 🌱
