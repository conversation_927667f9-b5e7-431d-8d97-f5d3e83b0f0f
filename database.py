import sqlite3
import os
from datetime import datetime

class Database:
    def __init__(self, db_name="pharmacy.db"):
        self.db_name = db_name
        self.init_database()
    
    def get_connection(self):
        return sqlite3.connect(self.db_name)
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                category TEXT NOT NULL,
                price REAL NOT NULL,
                quantity INTEGER NOT NULL,
                min_quantity INTEGER DEFAULT 10,
                supplier TEXT,
                expiry_date TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المبيعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER,
                total_amount REAL NOT NULL,
                sale_date TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')
        
        # جدول تفاصيل المبيعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER,
                product_id INTEGER,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (sale_id) REFERENCES sales (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_product(self, name, category, price, quantity, min_quantity=10, supplier="", expiry_date=""):
        """إضافة منتج جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO products (name, category, price, quantity, min_quantity, supplier, expiry_date)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (name, category, price, quantity, min_quantity, supplier, expiry_date))
        conn.commit()
        conn.close()
    
    def get_all_products(self):
        """الحصول على جميع المنتجات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM products ORDER BY name')
        products = cursor.fetchall()
        conn.close()
        return products
    
    def update_product(self, product_id, name, category, price, quantity, min_quantity, supplier, expiry_date):
        """تحديث منتج"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE products 
            SET name=?, category=?, price=?, quantity=?, min_quantity=?, supplier=?, expiry_date=?
            WHERE id=?
        ''', (name, category, price, quantity, min_quantity, supplier, expiry_date, product_id))
        conn.commit()
        conn.close()
    
    def delete_product(self, product_id):
        """حذف منتج"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('DELETE FROM products WHERE id=?', (product_id,))
        conn.commit()
        conn.close()
    
    def search_products(self, search_term):
        """البحث عن المنتجات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT * FROM products 
            WHERE name LIKE ? OR category LIKE ? OR supplier LIKE ?
            ORDER BY name
        ''', (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))
        products = cursor.fetchall()
        conn.close()
        return products
    
    def add_customer(self, name, phone="", address=""):
        """إضافة عميل جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO customers (name, phone, address)
            VALUES (?, ?, ?)
        ''', (name, phone, address))
        conn.commit()
        conn.close()
    
    def get_all_customers(self):
        """الحصول على جميع العملاء"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM customers ORDER BY name')
        customers = cursor.fetchall()
        conn.close()
        return customers
    
    def add_sale(self, customer_id, items):
        """إضافة عملية بيع جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # حساب المجموع الكلي
        total_amount = sum(item['quantity'] * item['price'] for item in items)
        
        # إضافة البيع
        cursor.execute('''
            INSERT INTO sales (customer_id, total_amount)
            VALUES (?, ?)
        ''', (customer_id, total_amount))
        
        sale_id = cursor.lastrowid
        
        # إضافة تفاصيل البيع وتحديث المخزون
        for item in items:
            cursor.execute('''
                INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?)
            ''', (sale_id, item['product_id'], item['quantity'], item['price'], 
                  item['quantity'] * item['price']))
            
            # تحديث كمية المنتج في المخزون
            cursor.execute('''
                UPDATE products SET quantity = quantity - ? WHERE id = ?
            ''', (item['quantity'], item['product_id']))
        
        conn.commit()
        conn.close()
        return sale_id
    
    def get_low_stock_products(self):
        """الحصول على المنتجات منخفضة المخزون"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM products WHERE quantity <= min_quantity')
        products = cursor.fetchall()
        conn.close()
        return products
    
    def get_sales_report(self, start_date=None, end_date=None):
        """تقرير المبيعات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        if start_date and end_date:
            cursor.execute('''
                SELECT s.id, c.name, s.total_amount, s.sale_date
                FROM sales s
                LEFT JOIN customers c ON s.customer_id = c.id
                WHERE DATE(s.sale_date) BETWEEN ? AND ?
                ORDER BY s.sale_date DESC
            ''', (start_date, end_date))
        else:
            cursor.execute('''
                SELECT s.id, c.name, s.total_amount, s.sale_date
                FROM sales s
                LEFT JOIN customers c ON s.customer_id = c.id
                ORDER BY s.sale_date DESC
            ''')
        
        sales = cursor.fetchall()
        conn.close()
        return sales
