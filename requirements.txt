# متطلبات برنامج الصيدلية الزراعية
# Agricultural Pharmacy Management System Requirements

# واجهة المستخدم الرسومية (مدمجة مع Python)
# tkinter - GUI framework (built-in)

# قاعدة البيانات (مدمجة مع Python)
# sqlite3 - Database (built-in)

# التاريخ والوقت (مدمج مع Python)
# datetime - Date and time handling (built-in)

# تحويل البرنامج إلى exe
pyinstaller>=5.0

# مكتبات إضافية اختيارية للتطوير المستقبلي
# reportlab>=3.6.0  # لإنشاء تقارير PDF
# pillow>=9.0.0     # لمعالجة الصور
# openpyxl>=3.0.0   # لتصدير Excel

# ملاحظة: المكتبات المعلقة بـ # اختيارية ويمكن تثبيتها عند الحاجة
