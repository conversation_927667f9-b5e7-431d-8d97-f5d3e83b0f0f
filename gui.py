import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime, date
import os
from database import Database
from models import PRODUCT_CATEGORIES, UNITS

class PharmacyGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("برنامج إدارة الصيدلية الزراعية")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # إنشاء قاعدة البيانات
        self.db = Database()
        
        # متغيرات للبحث والفلترة
        self.search_var = tk.StringVar()
        self.category_filter = tk.StringVar()
        
        # سلة التسوق للمبيعات
        self.cart = []
        
        self.create_widgets()
        self.refresh_products()
    
    def create_widgets(self):
        """إنشاء واجهة المستخدم"""
        # إنشاء النوتبوك للتبويبات
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # تبويب المنتجات
        self.products_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.products_frame, text="إدارة المنتجات")
        self.create_products_tab()
        
        # تبويب المبيعات
        self.sales_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.sales_frame, text="المبيعات")
        self.create_sales_tab()
        
        # تبويب العملاء
        self.customers_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.customers_frame, text="العملاء")
        self.create_customers_tab()
        
        # تبويب التقارير
        self.reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.reports_frame, text="التقارير")
        self.create_reports_tab()
    
    def create_products_tab(self):
        """إنشاء تبويب المنتجات"""
        # إطار البحث والفلترة
        search_frame = ttk.LabelFrame(self.products_frame, text="البحث والفلترة")
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(search_frame, text="البحث:").grid(row=0, column=0, padx=5, pady=5)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.grid(row=0, column=1, padx=5, pady=5)
        search_entry.bind('<KeyRelease>', self.search_products)
        
        ttk.Label(search_frame, text="الفئة:").grid(row=0, column=2, padx=5, pady=5)
        category_combo = ttk.Combobox(search_frame, textvariable=self.category_filter, 
                                     values=["الكل"] + PRODUCT_CATEGORIES, state="readonly")
        category_combo.grid(row=0, column=3, padx=5, pady=5)
        category_combo.set("الكل")
        category_combo.bind('<<ComboboxSelected>>', self.filter_products)
        
        # أزرار الإجراءات
        buttons_frame = ttk.Frame(self.products_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="إضافة منتج", command=self.add_product_dialog).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="تعديل منتج", command=self.edit_product_dialog).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="حذف منتج", command=self.delete_product).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="تحديث", command=self.refresh_products).pack(side=tk.LEFT, padx=5)
        
        # جدول المنتجات
        self.products_tree = ttk.Treeview(self.products_frame, columns=(
            "ID", "الاسم", "الفئة", "السعر", "الكمية", "الحد الأدنى", "المورد", "تاريخ الانتهاء"
        ), show="headings", height=20)
        
        # تعريف الأعمدة
        self.products_tree.heading("ID", text="الرقم")
        self.products_tree.heading("الاسم", text="اسم المنتج")
        self.products_tree.heading("الفئة", text="الفئة")
        self.products_tree.heading("السعر", text="السعر")
        self.products_tree.heading("الكمية", text="الكمية")
        self.products_tree.heading("الحد الأدنى", text="الحد الأدنى")
        self.products_tree.heading("المورد", text="المورد")
        self.products_tree.heading("تاريخ الانتهاء", text="تاريخ الانتهاء")
        
        # تحديد عرض الأعمدة
        self.products_tree.column("ID", width=50)
        self.products_tree.column("الاسم", width=200)
        self.products_tree.column("الفئة", width=120)
        self.products_tree.column("السعر", width=80)
        self.products_tree.column("الكمية", width=80)
        self.products_tree.column("الحد الأدنى", width=80)
        self.products_tree.column("المورد", width=150)
        self.products_tree.column("تاريخ الانتهاء", width=120)
        
        # شريط التمرير
        products_scrollbar = ttk.Scrollbar(self.products_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=products_scrollbar.set)
        
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        products_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_sales_tab(self):
        """إنشاء تبويب المبيعات"""
        # إطار اختيار المنتجات
        selection_frame = ttk.LabelFrame(self.sales_frame, text="اختيار المنتجات")
        selection_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # قائمة المنتجات للبيع
        products_frame = ttk.Frame(selection_frame)
        products_frame.pack(fill=tk.BOTH, expand=True)
        
        self.sales_products_tree = ttk.Treeview(products_frame, columns=(
            "ID", "الاسم", "السعر", "الكمية المتاحة"
        ), show="headings", height=8)
        
        self.sales_products_tree.heading("ID", text="الرقم")
        self.sales_products_tree.heading("الاسم", text="اسم المنتج")
        self.sales_products_tree.heading("السعر", text="السعر")
        self.sales_products_tree.heading("الكمية المتاحة", text="الكمية المتاحة")
        
        self.sales_products_tree.column("ID", width=50)
        self.sales_products_tree.column("الاسم", width=250)
        self.sales_products_tree.column("السعر", width=100)
        self.sales_products_tree.column("الكمية المتاحة", width=100)
        
        self.sales_products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # أزرار إضافة للسلة
        add_frame = ttk.Frame(selection_frame)
        add_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(add_frame, text="الكمية:").pack(side=tk.LEFT, padx=5)
        self.quantity_var = tk.StringVar(value="1")
        quantity_entry = ttk.Entry(add_frame, textvariable=self.quantity_var, width=10)
        quantity_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(add_frame, text="إضافة للسلة", command=self.add_to_cart).pack(side=tk.LEFT, padx=5)
        
        # سلة التسوق
        cart_frame = ttk.LabelFrame(self.sales_frame, text="سلة التسوق")
        cart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.cart_tree = ttk.Treeview(cart_frame, columns=(
            "المنتج", "الكمية", "السعر", "المجموع"
        ), show="headings", height=8)
        
        self.cart_tree.heading("المنتج", text="المنتج")
        self.cart_tree.heading("الكمية", text="الكمية")
        self.cart_tree.heading("السعر", text="السعر")
        self.cart_tree.heading("المجموع", text="المجموع")
        
        self.cart_tree.pack(fill=tk.BOTH, expand=True)
        
        # إطار المجموع والدفع
        total_frame = ttk.Frame(self.sales_frame)
        total_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.total_label = ttk.Label(total_frame, text="المجموع الكلي: 0.00", font=("Arial", 14, "bold"))
        self.total_label.pack(side=tk.RIGHT, padx=10)
        
        ttk.Button(total_frame, text="إتمام البيع", command=self.complete_sale).pack(side=tk.LEFT, padx=5)
        ttk.Button(total_frame, text="مسح السلة", command=self.clear_cart).pack(side=tk.LEFT, padx=5)
        ttk.Button(total_frame, text="حذف من السلة", command=self.remove_from_cart).pack(side=tk.LEFT, padx=5)
        
        self.refresh_sales_products()
    
    def create_customers_tab(self):
        """إنشاء تبويب العملاء"""
        # أزرار الإجراءات
        buttons_frame = ttk.Frame(self.customers_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="إضافة عميل", command=self.add_customer_dialog).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="تحديث", command=self.refresh_customers).pack(side=tk.LEFT, padx=5)
        
        # جدول العملاء
        self.customers_tree = ttk.Treeview(self.customers_frame, columns=(
            "ID", "الاسم", "الهاتف", "العنوان"
        ), show="headings", height=20)
        
        self.customers_tree.heading("ID", text="الرقم")
        self.customers_tree.heading("الاسم", text="اسم العميل")
        self.customers_tree.heading("الهاتف", text="الهاتف")
        self.customers_tree.heading("العنوان", text="العنوان")
        
        self.customers_tree.column("ID", width=50)
        self.customers_tree.column("الاسم", width=200)
        self.customers_tree.column("الهاتف", width=150)
        self.customers_tree.column("العنوان", width=300)
        
        self.customers_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.refresh_customers()
    
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        # أزرار التقارير
        buttons_frame = ttk.Frame(self.reports_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="تقرير المخزون", command=self.show_inventory_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="المنتجات منخفضة المخزون", command=self.show_low_stock_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="تقرير المبيعات", command=self.show_sales_report).pack(side=tk.LEFT, padx=5)
        
        # منطقة عرض التقارير
        self.reports_text = tk.Text(self.reports_frame, height=25, width=80)
        self.reports_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # شريط التمرير للتقارير
        reports_scrollbar = ttk.Scrollbar(self.reports_frame, orient=tk.VERTICAL, command=self.reports_text.yview)
        self.reports_text.configure(yscrollcommand=reports_scrollbar.set)
        reports_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def refresh_products(self):
        """تحديث قائمة المنتجات"""
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)

        products = self.db.get_all_products()
        for product in products:
            # تلوين المنتجات منخفضة المخزون
            tags = ()
            if product[4] <= product[5]:  # الكمية <= الحد الأدنى
                tags = ('low_stock',)

            self.products_tree.insert("", tk.END, values=product, tags=tags)

        # تعريف ألوان للمنتجات منخفضة المخزون
        self.products_tree.tag_configure('low_stock', background='#ffcccc')

    def search_products(self, event=None):
        """البحث في المنتجات"""
        search_term = self.search_var.get()

        for item in self.products_tree.get_children():
            self.products_tree.delete(item)

        if search_term:
            products = self.db.search_products(search_term)
        else:
            products = self.db.get_all_products()

        for product in products:
            tags = ()
            if product[4] <= product[5]:
                tags = ('low_stock',)
            self.products_tree.insert("", tk.END, values=product, tags=tags)

        self.products_tree.tag_configure('low_stock', background='#ffcccc')

    def filter_products(self, event=None):
        """فلترة المنتجات حسب الفئة"""
        category = self.category_filter.get()

        for item in self.products_tree.get_children():
            self.products_tree.delete(item)

        products = self.db.get_all_products()

        for product in products:
            if category == "الكل" or product[2] == category:
                tags = ()
                if product[4] <= product[5]:
                    tags = ('low_stock',)
                self.products_tree.insert("", tk.END, values=product, tags=tags)

        self.products_tree.tag_configure('low_stock', background='#ffcccc')

    def add_product_dialog(self):
        """حوار إضافة منتج جديد"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة منتج جديد")
        dialog.geometry("400x500")
        dialog.resizable(False, False)

        # متغيرات النموذج
        name_var = tk.StringVar()
        category_var = tk.StringVar()
        price_var = tk.StringVar()
        quantity_var = tk.StringVar()
        min_quantity_var = tk.StringVar(value="10")
        supplier_var = tk.StringVar()
        expiry_var = tk.StringVar()

        # حقول الإدخال
        ttk.Label(dialog, text="اسم المنتج:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        ttk.Entry(dialog, textvariable=name_var, width=30).grid(row=0, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="الفئة:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        category_combo = ttk.Combobox(dialog, textvariable=category_var, values=PRODUCT_CATEGORIES, state="readonly")
        category_combo.grid(row=1, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="السعر:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        ttk.Entry(dialog, textvariable=price_var, width=30).grid(row=2, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="الكمية:").grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        ttk.Entry(dialog, textvariable=quantity_var, width=30).grid(row=3, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="الحد الأدنى:").grid(row=4, column=0, sticky=tk.W, padx=10, pady=5)
        ttk.Entry(dialog, textvariable=min_quantity_var, width=30).grid(row=4, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="المورد:").grid(row=5, column=0, sticky=tk.W, padx=10, pady=5)
        ttk.Entry(dialog, textvariable=supplier_var, width=30).grid(row=5, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="تاريخ الانتهاء (YYYY-MM-DD):").grid(row=6, column=0, sticky=tk.W, padx=10, pady=5)
        ttk.Entry(dialog, textvariable=expiry_var, width=30).grid(row=6, column=1, padx=10, pady=5)

        def save_product():
            try:
                name = name_var.get().strip()
                category = category_var.get()
                price = float(price_var.get())
                quantity = int(quantity_var.get())
                min_quantity = int(min_quantity_var.get())
                supplier = supplier_var.get().strip()
                expiry = expiry_var.get().strip()

                if not name or not category:
                    messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                    return

                self.db.add_product(name, category, price, quantity, min_quantity, supplier, expiry)
                messagebox.showinfo("نجح", "تم إضافة المنتج بنجاح")
                dialog.destroy()
                self.refresh_products()
                self.refresh_sales_products()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى التأكد من صحة البيانات المدخلة")

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(dialog)
        buttons_frame.grid(row=7, column=0, columnspan=2, pady=20)

        ttk.Button(buttons_frame, text="حفظ", command=save_product).pack(side=tk.LEFT, padx=10)
        ttk.Button(buttons_frame, text="إلغاء", command=dialog.destroy).pack(side=tk.LEFT, padx=10)

    def edit_product_dialog(self):
        """حوار تعديل منتج"""
        selected = self.products_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للتعديل")
            return

        item = self.products_tree.item(selected[0])
        product_data = item['values']

        dialog = tk.Toplevel(self.root)
        dialog.title("تعديل منتج")
        dialog.geometry("400x500")
        dialog.resizable(False, False)

        # متغيرات النموذج مع القيم الحالية
        name_var = tk.StringVar(value=product_data[1])
        category_var = tk.StringVar(value=product_data[2])
        price_var = tk.StringVar(value=str(product_data[3]))
        quantity_var = tk.StringVar(value=str(product_data[4]))
        min_quantity_var = tk.StringVar(value=str(product_data[5]))
        supplier_var = tk.StringVar(value=product_data[6])
        expiry_var = tk.StringVar(value=product_data[7])

        # حقول الإدخال (نفس التصميم كما في إضافة منتج)
        ttk.Label(dialog, text="اسم المنتج:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        ttk.Entry(dialog, textvariable=name_var, width=30).grid(row=0, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="الفئة:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        category_combo = ttk.Combobox(dialog, textvariable=category_var, values=PRODUCT_CATEGORIES, state="readonly")
        category_combo.grid(row=1, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="السعر:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        ttk.Entry(dialog, textvariable=price_var, width=30).grid(row=2, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="الكمية:").grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        ttk.Entry(dialog, textvariable=quantity_var, width=30).grid(row=3, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="الحد الأدنى:").grid(row=4, column=0, sticky=tk.W, padx=10, pady=5)
        ttk.Entry(dialog, textvariable=min_quantity_var, width=30).grid(row=4, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="المورد:").grid(row=5, column=0, sticky=tk.W, padx=10, pady=5)
        ttk.Entry(dialog, textvariable=supplier_var, width=30).grid(row=5, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="تاريخ الانتهاء (YYYY-MM-DD):").grid(row=6, column=0, sticky=tk.W, padx=10, pady=5)
        ttk.Entry(dialog, textvariable=expiry_var, width=30).grid(row=6, column=1, padx=10, pady=5)

        def update_product():
            try:
                name = name_var.get().strip()
                category = category_var.get()
                price = float(price_var.get())
                quantity = int(quantity_var.get())
                min_quantity = int(min_quantity_var.get())
                supplier = supplier_var.get().strip()
                expiry = expiry_var.get().strip()

                if not name or not category:
                    messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                    return

                self.db.update_product(product_data[0], name, category, price, quantity, min_quantity, supplier, expiry)
                messagebox.showinfo("نجح", "تم تحديث المنتج بنجاح")
                dialog.destroy()
                self.refresh_products()
                self.refresh_sales_products()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى التأكد من صحة البيانات المدخلة")

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(dialog)
        buttons_frame.grid(row=7, column=0, columnspan=2, pady=20)

        ttk.Button(buttons_frame, text="تحديث", command=update_product).pack(side=tk.LEFT, padx=10)
        ttk.Button(buttons_frame, text="إلغاء", command=dialog.destroy).pack(side=tk.LEFT, padx=10)

    def delete_product(self):
        """حذف منتج"""
        selected = self.products_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للحذف")
            return

        item = self.products_tree.item(selected[0])
        product_name = item['values'][1]
        product_id = item['values'][0]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المنتج '{product_name}'؟"):
            self.db.delete_product(product_id)
            messagebox.showinfo("نجح", "تم حذف المنتج بنجاح")
            self.refresh_products()
            self.refresh_sales_products()

    def refresh_sales_products(self):
        """تحديث قائمة المنتجات للبيع"""
        for item in self.sales_products_tree.get_children():
            self.sales_products_tree.delete(item)

        products = self.db.get_all_products()
        for product in products:
            if product[4] > 0:  # فقط المنتجات المتوفرة
                self.sales_products_tree.insert("", tk.END, values=(
                    product[0], product[1], product[3], product[4]
                ))

    def add_to_cart(self):
        """إضافة منتج للسلة"""
        selected = self.sales_products_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج")
            return

        try:
            quantity = int(self.quantity_var.get())
            if quantity <= 0:
                messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر")
                return
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
            return

        item = self.sales_products_tree.item(selected[0])
        product_data = item['values']
        product_id = product_data[0]
        product_name = product_data[1]
        price = float(product_data[2])
        available_quantity = int(product_data[3])

        if quantity > available_quantity:
            messagebox.showerror("خطأ", f"الكمية المطلوبة أكبر من المتوفر ({available_quantity})")
            return

        # التحقق من وجود المنتج في السلة
        for i, cart_item in enumerate(self.cart):
            if cart_item['product_id'] == product_id:
                new_quantity = cart_item['quantity'] + quantity
                if new_quantity > available_quantity:
                    messagebox.showerror("خطأ", f"إجمالي الكمية في السلة سيتجاوز المتوفر ({available_quantity})")
                    return
                self.cart[i]['quantity'] = new_quantity
                break
        else:
            # إضافة منتج جديد للسلة
            self.cart.append({
                'product_id': product_id,
                'name': product_name,
                'price': price,
                'quantity': quantity
            })

        self.update_cart_display()
        self.quantity_var.set("1")

    def update_cart_display(self):
        """تحديث عرض السلة"""
        for item in self.cart_tree.get_children():
            self.cart_tree.delete(item)

        total = 0
        for cart_item in self.cart:
            item_total = cart_item['quantity'] * cart_item['price']
            total += item_total
            self.cart_tree.insert("", tk.END, values=(
                cart_item['name'],
                cart_item['quantity'],
                f"{cart_item['price']:.2f}",
                f"{item_total:.2f}"
            ))

        self.total_label.config(text=f"المجموع الكلي: {total:.2f}")

    def remove_from_cart(self):
        """حذف منتج من السلة"""
        selected = self.cart_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج من السلة")
            return

        item_index = self.cart_tree.index(selected[0])
        del self.cart[item_index]
        self.update_cart_display()

    def clear_cart(self):
        """مسح السلة"""
        if self.cart and messagebox.askyesno("تأكيد", "هل أنت متأكد من مسح السلة؟"):
            self.cart.clear()
            self.update_cart_display()

    def complete_sale(self):
        """إتمام عملية البيع"""
        if not self.cart:
            messagebox.showwarning("تحذير", "السلة فارغة")
            return

        # اختيار العميل (اختياري)
        customer_id = None
        if messagebox.askyesno("عميل", "هل تريد ربط البيع بعميل؟"):
            customers = self.db.get_all_customers()
            if not customers:
                messagebox.showinfo("معلومة", "لا يوجد عملاء مسجلين")
            else:
                customer_names = [f"{c[0]} - {c[1]}" for c in customers]
                customer_choice = simpledialog.askstring("اختيار العميل",
                    f"اختر العميل:\n" + "\n".join(customer_names) + "\nأدخل الرقم:")
                if customer_choice and customer_choice.isdigit():
                    customer_id = int(customer_choice)

        try:
            # إضافة البيع لقاعدة البيانات
            sale_id = self.db.add_sale(customer_id, self.cart)

            # عرض رسالة نجاح
            total = sum(item['quantity'] * item['price'] for item in self.cart)
            messagebox.showinfo("نجح", f"تم إتمام البيع بنجاح\nرقم الفاتورة: {sale_id}\nالمجموع: {total:.2f}")

            # مسح السلة وتحديث العرض
            self.cart.clear()
            self.update_cart_display()
            self.refresh_products()
            self.refresh_sales_products()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إتمام البيع: {str(e)}")

    def add_customer_dialog(self):
        """حوار إضافة عميل جديد"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة عميل جديد")
        dialog.geometry("400x300")
        dialog.resizable(False, False)

        # متغيرات النموذج
        name_var = tk.StringVar()
        phone_var = tk.StringVar()
        address_var = tk.StringVar()

        # حقول الإدخال
        ttk.Label(dialog, text="اسم العميل:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=10)
        ttk.Entry(dialog, textvariable=name_var, width=30).grid(row=0, column=1, padx=10, pady=10)

        ttk.Label(dialog, text="رقم الهاتف:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=10)
        ttk.Entry(dialog, textvariable=phone_var, width=30).grid(row=1, column=1, padx=10, pady=10)

        ttk.Label(dialog, text="العنوان:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=10)
        address_text = tk.Text(dialog, width=30, height=4)
        address_text.grid(row=2, column=1, padx=10, pady=10)

        def save_customer():
            name = name_var.get().strip()
            phone = phone_var.get().strip()
            address = address_text.get("1.0", tk.END).strip()

            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
                return

            self.db.add_customer(name, phone, address)
            messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
            dialog.destroy()
            self.refresh_customers()

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(dialog)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=20)

        ttk.Button(buttons_frame, text="حفظ", command=save_customer).pack(side=tk.LEFT, padx=10)
        ttk.Button(buttons_frame, text="إلغاء", command=dialog.destroy).pack(side=tk.LEFT, padx=10)

    def refresh_customers(self):
        """تحديث قائمة العملاء"""
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)

        customers = self.db.get_all_customers()
        for customer in customers:
            self.customers_tree.insert("", tk.END, values=customer[:4])  # عرض أول 4 أعمدة فقط

    def show_inventory_report(self):
        """عرض تقرير المخزون"""
        self.reports_text.delete("1.0", tk.END)

        products = self.db.get_all_products()

        report = "تقرير المخزون\n"
        report += "=" * 50 + "\n\n"
        report += f"{'اسم المنتج':<30} {'الفئة':<15} {'الكمية':<10} {'السعر':<10}\n"
        report += "-" * 65 + "\n"

        total_value = 0
        for product in products:
            name = product[1][:28]  # قطع الاسم إذا كان طويلاً
            category = product[2][:13]
            quantity = product[4]
            price = product[3]
            value = quantity * price
            total_value += value

            report += f"{name:<30} {category:<15} {quantity:<10} {price:<10.2f}\n"

        report += "-" * 65 + "\n"
        report += f"إجمالي قيمة المخزون: {total_value:.2f}\n"
        report += f"عدد المنتجات: {len(products)}\n"

        self.reports_text.insert("1.0", report)

    def show_low_stock_report(self):
        """عرض تقرير المنتجات منخفضة المخزون"""
        self.reports_text.delete("1.0", tk.END)

        low_stock_products = self.db.get_low_stock_products()

        report = "تقرير المنتجات منخفضة المخزون\n"
        report += "=" * 50 + "\n\n"

        if not low_stock_products:
            report += "لا توجد منتجات منخفضة المخزون\n"
        else:
            report += f"{'اسم المنتج':<30} {'الكمية الحالية':<15} {'الحد الأدنى':<15}\n"
            report += "-" * 60 + "\n"

            for product in low_stock_products:
                name = product[1][:28]
                current_qty = product[4]
                min_qty = product[5]

                report += f"{name:<30} {current_qty:<15} {min_qty:<15}\n"

        self.reports_text.insert("1.0", report)

    def show_sales_report(self):
        """عرض تقرير المبيعات"""
        self.reports_text.delete("1.0", tk.END)

        sales = self.db.get_sales_report()

        report = "تقرير المبيعات\n"
        report += "=" * 50 + "\n\n"

        if not sales:
            report += "لا توجد مبيعات مسجلة\n"
        else:
            report += f"{'رقم الفاتورة':<15} {'العميل':<20} {'المبلغ':<15} {'التاريخ':<20}\n"
            report += "-" * 70 + "\n"

            total_sales = 0
            for sale in sales:
                sale_id = sale[0]
                customer_name = sale[1] if sale[1] else "غير محدد"
                amount = sale[2]
                sale_date = sale[3][:16]  # عرض التاريخ والوقت فقط
                total_sales += amount

                customer_name = customer_name[:18] if customer_name else "غير محدد"

                report += f"{sale_id:<15} {customer_name:<20} {amount:<15.2f} {sale_date:<20}\n"

            report += "-" * 70 + "\n"
            report += f"إجمالي المبيعات: {total_sales:.2f}\n"
            report += f"عدد الفواتير: {len(sales)}\n"

        self.reports_text.insert("1.0", report)
