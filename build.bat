@echo off
chcp 65001 > nul
title بناء برنامج الصيدلية الزراعية

echo.
echo ========================================
echo    بناء برنامج الصيدلية الزراعية
echo    Building Agricultural Pharmacy
echo ========================================
echo.

REM التحقق من وجود Python
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo.

REM تثبيت PyInstaller إذا لم يكن مثبتاً
echo 📦 التحقق من PyInstaller...
python -c "import PyInstaller" > nul 2>&1
if %errorlevel% neq 0 (
    echo 📥 تثبيت PyInstaller...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت PyInstaller
        pause
        exit /b 1
    )
)

echo ✅ PyInstaller جاهز
echo.

REM تشغيل سكريبت البناء
echo 🔨 بدء عملية البناء...
python build_exe.py

echo.
echo 🎉 انتهت عملية البناء
echo 📁 تحقق من مجلد dist للحصول على الملف التنفيذي
echo.
pause
